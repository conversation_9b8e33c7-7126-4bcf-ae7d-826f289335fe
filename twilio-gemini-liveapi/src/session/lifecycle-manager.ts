import { WebSocket } from 'ws';
import { ConnectionData } from '../types/global.js';
import { ContextManager } from './context-manager.js';
import { SessionSummaryManager } from './summary-manager.js';
import { ConnectionHealthMonitor } from './health-monitor.js';
import { SessionManager } from './session-manager.js';

interface SessionState {
    sessionId: string;
    startTime: number;
    lastActivity: number;
    isActive: boolean;
    userControlled: boolean;
    keepAliveActive: boolean;
    endRequested: boolean;
    summaryRequested: boolean;
    sessionConfig: Record<string, any>;
    endTime?: number;
    endReason?: string;
}

// Session Lifecycle Manager - handles session persistence and proper ending
export class SessionLifecycleManager {
    private contextManager: ContextManager;
    private summaryManager: SessionSummaryManager;
    private healthMonitor: ConnectionHealthMonitor;
    private sessionManager?: SessionManager;
    private keepAliveIntervals: Map<string, NodeJS.Timeout>;
    private sessionStates: Map<string, SessionState>;
    private keepAliveInterval: number;
    private maxInactivityTime: number;

    constructor(contextManager: ContextManager, summaryManager: SessionSummaryManager, healthMonitor: ConnectionHealthMonitor) {
        this.contextManager = contextManager;
        this.summaryManager = summaryManager;
        this.healthMonitor = healthMonitor;
        this.keepAliveIntervals = new Map(); // Track keep-alive intervals
        this.sessionStates = new Map(); // Track session states
        this.keepAliveInterval = 30000; // 30 seconds keep-alive interval
        this.maxInactivityTime = 300000; // 5 minutes max inactivity before cleanup
    }

    /**
     * Start session lifecycle management
     * @param sessionId - Session ID (callSid or local session ID)
     * @param connectionData - Connection data
     * @param sessionConfig - Session configuration
     */
    startSession(sessionId: string, connectionData: ConnectionData, sessionConfig?: Record<string, any>): void {
        console.log(`🚀 [${sessionId}] Starting session lifecycle management`);

        // Initialize session state
        const sessionState: SessionState = {
            sessionId,
            startTime: Date.now(),
            lastActivity: Date.now(),
            isActive: true,
            userControlled: true, // User controls session lifecycle, not AI
            keepAliveActive: false,
            endRequested: false,
            summaryRequested: false,
            sessionConfig: sessionConfig || {}
        };

        this.sessionStates.set(sessionId, sessionState);

        // Start keep-alive mechanism
        this.startKeepAlive(sessionId, connectionData);

        // Save initial context
        if (this.contextManager) {
            this.contextManager.saveSessionContext(sessionId, {
                ...connectionData,
                ...sessionConfig,
                isSessionActive: true,
                sessionStartTime: sessionState.startTime,
                keepAliveActive: true
            });
        }

        console.log(`✅ [${sessionId}] Session lifecycle management started`);
    }

    /**
     * Start keep-alive mechanism for session persistence
     * @param sessionId - Session ID
     * @param connectionData - Connection data
     */
    private startKeepAlive(sessionId: string, connectionData: ConnectionData): void {
        // Clear any existing keep-alive
        this.stopKeepAlive(sessionId);

        const keepAliveId = setInterval(() => {
            this.performKeepAlive(sessionId, connectionData);
        }, this.keepAliveInterval);

        this.keepAliveIntervals.set(sessionId, keepAliveId);

        const sessionState = this.sessionStates.get(sessionId);
        if (sessionState) {
            sessionState.keepAliveActive = true;
        }

        console.log(`💓 [${sessionId}] Keep-alive started (interval: ${this.keepAliveInterval}ms)`);
    }

    /**
     * Perform keep-alive check and maintenance
     * @param sessionId - Session ID
     * @param connectionData - Connection data
     */
    private performKeepAlive(sessionId: string, connectionData: ConnectionData): void {
        const sessionState = this.sessionStates.get(sessionId);
        if (!sessionState || !sessionState.isActive) {
            console.log(`⏹️ [${sessionId}] Session inactive, stopping keep-alive`);
            this.stopKeepAlive(sessionId);
            return;
        }

        const now = Date.now();
        const timeSinceLastActivity = now - sessionState.lastActivity;

        // ENHANCED: Sessions persist indefinitely until manually stopped
        // No automatic timeout - user controls session lifecycle completely
        if (timeSinceLastActivity > this.maxInactivityTime) {
            console.log(`💤 [${sessionId}] Session inactive for ${Math.round(timeSinceLastActivity / 1000)}s, but maintaining session (user-controlled lifecycle)`);
            // Continue keeping session alive - only user can end it
        }

        // Check if Gemini session needs recovery
        if (connectionData && connectionData.geminiSession) {
            try {
                // Ping Gemini session to ensure it's still active
                const isGeminiActive = this.checkGeminiSessionHealth(connectionData.geminiSession);
                if (!isGeminiActive) {
                    console.log(`🔄 [${sessionId}] Gemini session appears inactive, may need recovery`);
                    // Mark for potential recovery but don't end session
                    if (this.contextManager) {
                        this.contextManager.markSessionInterrupted(sessionId, 'gemini_session_inactive');
                    }
                }
            } catch (error) {
                console.warn(`⚠️ [${sessionId}] Error checking Gemini session health:`, error);
            }
        }

        // Update health monitoring
        if (this.healthMonitor) {
            this.healthMonitor.trackConnection(sessionId, 'connected', {
                keepAlive: true,
                timeSinceLastActivity,
                sessionDuration: now - sessionState.startTime,
                userControlled: true
            });
        }

        // Save context periodically with enhanced data
        if (this.contextManager && connectionData) {
            this.contextManager.saveSessionContext(sessionId, {
                ...connectionData,
                isSessionActive: sessionState.isActive,
                lastActivity: now,
                keepAliveActive: true,
                sessionDuration: now - sessionState.startTime,
                userControlled: sessionState.userControlled,
                persistUntilUserStop: true
            });
        }

        console.log(`💓 [${sessionId}] Keep-alive performed (duration: ${Math.round((now - sessionState.startTime) / 1000)}s, inactive: ${Math.round(timeSinceLastActivity / 1000)}s)`);
    }

    /**
     * Check Gemini session health (basic connectivity check)
     * @param geminiSession - Gemini session object
     * @returns Whether session appears healthy
     */
    private checkGeminiSessionHealth(geminiSession: any): boolean {
        try {
            // Basic check - if session exists and has expected properties
            return geminiSession && typeof geminiSession.sendClientContent === 'function';
        } catch (error) {
            return false;
        }
    }

    /**
     * Update session activity timestamp
     * @param sessionId - Session ID
     */
    updateActivity(sessionId: string): void {
        const sessionState = this.sessionStates.get(sessionId);
        if (sessionState) {
            sessionState.lastActivity = Date.now();
        }
    }

    /**
     * Request session end (user-initiated)
     * @param sessionId - Session ID
     * @param connectionData - Connection data
     * @param reason - Reason for ending
     * @returns Success status
     */
    async requestSessionEnd(sessionId: string, connectionData: ConnectionData, reason: string = 'user_requested'): Promise<boolean> {
        console.log(`🛑 [${sessionId}] Session end requested by user (reason: ${reason})`);

        const sessionState = this.sessionStates.get(sessionId);
        if (!sessionState) {
            console.log(`⚠️ [${sessionId}] No session state found for end request`);
            return false;
        }

        if (sessionState.endRequested) {
            console.log(`⏳ [${sessionId}] Session end already in progress`);
            return true;
        }

        sessionState.endRequested = true;
        sessionState.endReason = reason;

        try {
            // Step 1: Request summary if conversation exists and not already requested
            if (!sessionState.summaryRequested && this.shouldGenerateSummary(connectionData)) {
                console.log(`📝 [${sessionId}] Requesting session summary before ending`);
                sessionState.summaryRequested = true;

                if (this.summaryManager) {
                    const summarySuccess = await this.summaryManager.requestSummary(
                        sessionId, 
                        connectionData, 
                        this.contextManager
                    );

                    if (summarySuccess) {
                        console.log(`✅ [${sessionId}] Summary requested successfully`);
                        // Set timeout to end session if summary takes too long
                        setTimeout(() => {
                            this.forceEndSession(sessionId, connectionData, 'summary_timeout');
                        }, 15000); // 15 second timeout
                        return true;
                    } else {
                        console.log(`⚠️ [${sessionId}] Summary request failed, proceeding with session end`);
                    }
                }
            }

            // Step 2: End session immediately if no summary needed or summary failed
            return await this.endSession(sessionId, connectionData, reason);

        } catch (error) {
            console.error(`❌ [${sessionId}] Error during session end request:`, error);
            return await this.forceEndSession(sessionId, connectionData, 'error');
        }
    }

    /**
     * End session (called after summary completion or timeout)
     * @param sessionId - Session ID
     * @param connectionData - Connection data
     * @param reason - Reason for ending
     * @returns Success status
     */
    async endSession(sessionId: string, connectionData: ConnectionData, reason: string = 'completed'): Promise<boolean> {
        console.log(`🔚 [${sessionId}] Ending session (reason: ${reason})`);

        const sessionState = this.sessionStates.get(sessionId);
        if (sessionState) {
            sessionState.isActive = false;
            sessionState.endTime = Date.now();
            sessionState.endReason = reason;
        }

        try {
            // Stop keep-alive
            this.stopKeepAlive(sessionId);

            // Close Gemini session if exists
            if (connectionData && connectionData.geminiSession) {
                try {
                    connectionData.geminiSession.close();
                    console.log(`🤖 [${sessionId}] Gemini session closed`);
                } catch (error) {
                    console.warn(`⚠️ [${sessionId}] Error closing Gemini session:`, error);
                }
            }

            // Close Twilio WebSocket if exists
            if (connectionData && connectionData.twilioWs) {
                try {
                    connectionData.twilioWs.close();
                    console.log(`📞 [${sessionId}] Twilio WebSocket closed`);
                } catch (error) {
                    console.warn(`⚠️ [${sessionId}] Error closing Twilio WebSocket:`, error);
                }
            }

            // Close transcription if exists
            if (connectionData && (connectionData as any).deepgramConnection) {
                try {
                    (connectionData as any).deepgramConnection.finish();
                    console.log(`🎤 [${sessionId}] Transcription connection closed`);
                } catch (error) {
                    console.warn(`⚠️ [${sessionId}] Error closing transcription:`, error);
                }
            }

            // Clean up summary tracking
            if (this.summaryManager) {
                this.summaryManager.cleanupSummary(sessionId, connectionData);
            }

            // Update health monitoring
            if (this.healthMonitor) {
                this.healthMonitor.trackConnection(sessionId, 'disconnected', {
                    reason,
                    sessionDuration: sessionState ? sessionState.endTime - sessionState.startTime : 0
                });
            }

            // Clear context
            if (this.contextManager) {
                this.contextManager.clearSessionContext(sessionId);
            }

            // Clean up session manager references
            if (this.sessionManager && this.sessionManager.cleanupSession) {
                this.sessionManager.cleanupSession(sessionId);
            }

            // Clean up session state
            this.sessionStates.delete(sessionId);

            console.log(`✅ [${sessionId}] Session ended successfully`);
            return true;

        } catch (error) {
            console.error(`❌ [${sessionId}] Error during session end:`, error);
            return false;
        }
    }

    /**
     * Force end session (emergency cleanup)
     * @param sessionId - Session ID
     * @param connectionData - Connection data
     * @param reason - Reason for force ending
     * @returns Success status
     */
    async forceEndSession(sessionId: string, connectionData: ConnectionData, reason: string = 'forced'): Promise<boolean> {
        console.log(`🚨 [${sessionId}] Force ending session (reason: ${reason})`);
        
        // Mark as inactive immediately
        const sessionState = this.sessionStates.get(sessionId);
        if (sessionState) {
            sessionState.isActive = false;
            sessionState.endTime = Date.now();
            sessionState.endReason = reason;
        }

        return await this.endSession(sessionId, connectionData, reason);
    }

    /**
     * Stop keep-alive for a session
     * @param sessionId - Session ID
     */
    private stopKeepAlive(sessionId: string): void {
        const keepAliveId = this.keepAliveIntervals.get(sessionId);
        if (keepAliveId) {
            clearInterval(keepAliveId);
            this.keepAliveIntervals.delete(sessionId);
            console.log(`💔 [${sessionId}] Keep-alive stopped`);
        }

        const sessionState = this.sessionStates.get(sessionId);
        if (sessionState) {
            sessionState.keepAliveActive = false;
        }
    }

    /**
     * Check if session should generate summary
     * @param connectionData - Connection data
     * @returns True if summary should be generated
     */
    private shouldGenerateSummary(connectionData: ConnectionData): boolean {
        if (!connectionData) return false;

        // Check if we have conversation data
        const hasConversation = ((connectionData as any).conversationLog && (connectionData as any).conversationLog.length > 0) ||
                               ((connectionData as any).fullTranscript && (connectionData as any).fullTranscript.length > 0) ||
                               ((connectionData as any).speechTranscript && (connectionData as any).speechTranscript.length > 0);

        return hasConversation && !(connectionData as any).summaryReceived;
    }

    /**
     * Get session status
     * @param sessionId - Session ID
     * @returns Session status
     */
    getSessionStatus(sessionId: string): {
        exists: boolean;
        sessionId?: string;
        isActive?: boolean;
        startTime?: number;
        lastActivity?: number;
        sessionDuration?: number;
        timeSinceLastActivity?: number;
        keepAliveActive?: boolean;
        endRequested?: boolean;
        summaryRequested?: boolean;
        endReason?: string | null;
        userControlled?: boolean;
    } {
        const sessionState = this.sessionStates.get(sessionId);
        if (!sessionState) {
            return { exists: false };
        }

        const now = Date.now();
        return {
            exists: true,
            sessionId: sessionState.sessionId,
            isActive: sessionState.isActive,
            startTime: sessionState.startTime,
            lastActivity: sessionState.lastActivity,
            sessionDuration: now - sessionState.startTime,
            timeSinceLastActivity: now - sessionState.lastActivity,
            keepAliveActive: sessionState.keepAliveActive,
            endRequested: sessionState.endRequested,
            summaryRequested: sessionState.summaryRequested,
            endReason: sessionState.endReason || null,
            userControlled: sessionState.userControlled
        };
    }

    /**
     * Get all active sessions
     * @returns Array of active session statuses
     */
    getActiveSessions(): Array<ReturnType<typeof this.getSessionStatus>> {
        const activeSessions: Array<ReturnType<typeof this.getSessionStatus>> = [];
        for (const [sessionId, sessionState] of this.sessionStates.entries()) {
            if (sessionState.isActive) {
                activeSessions.push(this.getSessionStatus(sessionId));
            }
        }
        return activeSessions;
    }

    /**
     * Clean up inactive sessions
     */
    cleanupInactiveSessions(): void {
        const now = Date.now();
        const maxAge = 3600000; // 1 hour

        for (const [sessionId, sessionState] of this.sessionStates.entries()) {
            if (!sessionState.isActive && (now - (sessionState.endTime || sessionState.startTime)) > maxAge) {
                this.sessionStates.delete(sessionId);
                this.stopKeepAlive(sessionId);
                console.log(`🧹 [${sessionId}] Cleaned up old inactive session`);
            }
        }
    }
}